#!/usr/bin/env python3
"""
查询净值历史数据库
"""

import asyncio
import sys
from datetime import datetime
from pathlib import Path
from loguru import logger

# 添加项目根目录到Python路径
sys.path.append(str(Path(__file__).parent))

from src.nm_crawl.database.connection import init_database, close_database, get_db_session
from src.nm_crawl.models.financial_models import HistoryNetValue
from src.nm_crawl.services.database_service import CmbDatabaseService
from sqlalchemy import select, func, desc


async def query_statistics():
    """查询统计信息"""
    logger.info("📊 数据库统计信息")
    logger.info("-" * 40)
    
    try:
        db_service = CmbDatabaseService()
        
        product_count = await db_service.get_product_count()
        history_count = await db_service.get_history_count()
        net_value_count = await db_service.get_net_value_count()
        
        logger.info(f"产品总数: {product_count}")
        logger.info(f"历史收益记录数: {history_count}")
        logger.info(f"净值记录数: {net_value_count}")
        
    except Exception as e:
        logger.error(f"查询统计信息失败: {e}")


async def query_net_value_by_product(rip_cod: str = "133030A", limit: int = 10):
    """查询指定产品的净值数据"""
    logger.info(f"📈 产品 {rip_cod} 的净值数据 (最近 {limit} 条)")
    logger.info("-" * 60)
    
    try:
        async for session in get_db_session():
            stmt = select(HistoryNetValue).where(
                HistoryNetValue.rip_cod == rip_cod
            ).order_by(desc(HistoryNetValue.net_value_date)).limit(limit)
            
            result = await session.execute(stmt)
            records = result.scalars().all()
            
            if not records:
                logger.warning(f"未找到产品 {rip_cod} 的净值数据")
                return
            
            logger.info(f"找到 {len(records)} 条记录:")
            logger.info(f"{'日期':<12} {'单位净值':<10} {'累计净值':<10} {'净值变化':<8} {'显示条款'}")
            logger.info("-" * 60)
            
            for record in records:
                show_provision = "是" if record.show_provision else "否"
                logger.info(f"{record.net_value_date:<12} {record.unit_net_value:<10} {record.total_net_value:<10} {record.net_value_change:<8} {show_provision}")
                
    except Exception as e:
        logger.error(f"查询净值数据失败: {e}")


async def query_net_value_range(start_date: str = "2025-07-01", end_date: str = "2025-08-31"):
    """查询指定日期范围的净值数据"""
    logger.info(f"📅 日期范围 {start_date} 到 {end_date} 的净值数据")
    logger.info("-" * 60)
    
    try:
        async for session in get_db_session():
            stmt = select(HistoryNetValue).where(
                HistoryNetValue.net_value_date >= start_date,
                HistoryNetValue.net_value_date <= end_date
            ).order_by(desc(HistoryNetValue.net_value_date))
            
            result = await session.execute(stmt)
            records = result.scalars().all()
            
            if not records:
                logger.warning(f"未找到指定日期范围的净值数据")
                return
            
            logger.info(f"找到 {len(records)} 条记录:")
            logger.info(f"{'产品代码':<10} {'日期':<12} {'单位净值':<10} {'累计净值':<10} {'净值变化':<8}")
            logger.info("-" * 60)
            
            for record in records[:20]:  # 只显示前20条
                logger.info(f"{record.rip_cod:<10} {record.net_value_date:<12} {record.unit_net_value:<10} {record.total_net_value:<10} {record.net_value_change:<8}")
            
            if len(records) > 20:
                logger.info(f"... 还有 {len(records) - 20} 条记录")
                
    except Exception as e:
        logger.error(f"查询日期范围数据失败: {e}")


async def query_net_value_summary():
    """查询净值数据汇总"""
    logger.info("📋 净值数据汇总")
    logger.info("-" * 40)
    
    try:
        async for session in get_db_session():
            # 查询每个产品的记录数
            stmt = select(
                HistoryNetValue.rip_cod,
                func.count(HistoryNetValue.id).label('record_count'),
                func.min(HistoryNetValue.net_value_date).label('earliest_date'),
                func.max(HistoryNetValue.net_value_date).label('latest_date'),
                func.min(HistoryNetValue.unit_net_value).label('min_net_value'),
                func.max(HistoryNetValue.unit_net_value).label('max_net_value')
            ).group_by(HistoryNetValue.rip_cod)
            
            result = await session.execute(stmt)
            records = result.all()
            
            if not records:
                logger.warning("未找到净值数据")
                return
            
            logger.info(f"{'产品代码':<10} {'记录数':<8} {'最早日期':<12} {'最新日期':<12} {'最小净值':<10} {'最大净值':<10}")
            logger.info("-" * 80)
            
            for record in records:
                logger.info(f"{record.rip_cod:<10} {record.record_count:<8} {record.earliest_date:<12} {record.latest_date:<12} {record.min_net_value:<10} {record.max_net_value:<10}")
                
    except Exception as e:
        logger.error(f"查询汇总数据失败: {e}")


async def main():
    """主函数"""
    logger.info("🏦 招商银行理财产品净值数据库查询")
    logger.info("=" * 60)
    
    try:
        # 初始化数据库
        await init_database()
        
        # 查询各种数据
        await query_statistics()
        print()
        
        await query_net_value_by_product("133030A", 10)
        print()
        
        await query_net_value_range("2025-07-01", "2025-08-31")
        print()
        
        await query_net_value_summary()
        print()
        
        logger.info("✅ 数据库查询完成！")
        logger.info("\n💡 提示:")
        logger.info("- 数据库文件位置: data/financial_products.db")
        logger.info("- 净值表名: history_netvalue")
        logger.info("- 可以使用SQLite工具进一步查询和分析")
        logger.info("- 示例查询: SELECT * FROM history_netvalue WHERE rip_cod='133030A' ORDER BY net_value_date DESC;")
        
        # 关闭数据库连接
        await close_database()
        
    except Exception as e:
        logger.error(f"数据库查询失败: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    # 设置简单日志
    logger.remove()
    logger.add(sys.stdout, level="INFO", format="{time:HH:mm:ss} | {level} | {message}")
    
    # 运行查询
    asyncio.run(main())

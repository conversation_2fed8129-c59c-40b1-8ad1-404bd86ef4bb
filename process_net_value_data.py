#!/usr/bin/env python3
"""
处理净值数据文件并写入数据库
"""

import asyncio
import json
import sys
import os
from pathlib import Path
from datetime import datetime

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from src.nm_crawl.database.connection import init_database, close_database
from src.nm_crawl.services.database_service import CmbDatabaseService
from loguru import logger


async def process_net_value_file(file_path: str):
    """
    处理单个净值数据文件
    
    Args:
        file_path: 文件路径
    """
    logger.info(f"Processing net value file: {file_path}")
    
    try:
        # 读取文件
        with open(file_path, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        # 从文件名中提取产品代码
        filename = Path(file_path).name
        # 文件名格式: history_profit_get-history-net-value_133030A_20250825_165906_764430.json
        parts = filename.split('_')
        rip_cod = None
        
        for i, part in enumerate(parts):
            if part == "get-history-net-value" and i + 1 < len(parts):
                rip_cod = parts[i + 1]
                break
        
        if not rip_cod:
            logger.error(f"Could not extract ripCod from filename: {filename}")
            return 0
        
        logger.info(f"Extracted product code: {rip_cod}")
        
        # 获取净值数据
        net_value_data = data.get('get-history-net-value', [])
        
        if not net_value_data:
            logger.warning(f"No net value data found in {file_path}")
            return 0
        
        logger.info(f"Found {len(net_value_data)} net value records")
        
        # 显示前几条数据
        logger.info("Sample data:")
        for i, record in enumerate(net_value_data[:3]):
            logger.info(f"  Record {i+1}: {record}")
        
        # 初始化数据库服务
        db_service = CmbDatabaseService()
        
        # 保存净值数据
        crawl_time = datetime.now()
        saved_count = await db_service.save_net_value_history(
            rip_cod,
            net_value_data,
            crawl_time
        )
        
        logger.info(f"Successfully saved {saved_count} net value records for product {rip_cod}")
        return saved_count
        
    except Exception as e:
        logger.error(f"Failed to process file {file_path}: {e}")
        import traceback
        traceback.print_exc()
        return 0


async def verify_data_in_database(rip_cod: str):
    """
    验证数据是否正确写入数据库
    
    Args:
        rip_cod: 产品代码
    """
    logger.info(f"Verifying data for product: {rip_cod}")
    
    try:
        from src.nm_crawl.database.connection import get_db_session
        from src.nm_crawl.models.financial_models import HistoryNetValue
        from sqlalchemy import select

        async for session in get_db_session():
            # 查询该产品的净值数据
            stmt = select(HistoryNetValue).where(
                HistoryNetValue.rip_cod == rip_cod
            ).order_by(HistoryNetValue.net_value_date.desc()).limit(5)

            result = await session.execute(stmt)
            records = result.scalars().all()

            logger.info(f"Found {len(records)} net value records in database")

            for i, record in enumerate(records):
                logger.info(f"Record {i+1}:")
                logger.info(f"  Date: {record.net_value_date}")
                logger.info(f"  Unit Net Value: {record.unit_net_value}")
                logger.info(f"  Total Net Value: {record.total_net_value}")
                logger.info(f"  Net Value Change: {record.net_value_change}")
                logger.info(f"  Show Provision: {record.show_provision}")

            # 统计总数
            count_stmt = select(HistoryNetValue).where(
                HistoryNetValue.rip_cod == rip_cod
            )
            count_result = await session.execute(count_stmt)
            total_count = len(count_result.scalars().all())

            logger.info(f"Total net value records for {rip_cod}: {total_count}")

            break
            
    except Exception as e:
        logger.error(f"Failed to verify data: {e}")
        import traceback
        traceback.print_exc()


async def main():
    """主函数"""
    print("处理净值数据文件")
    print("=" * 50)
    
    # 目标文件
    file_path = "data/history/history_profit_get-history-net-value_133030A_20250825_165906_764430.json"
    
    if not os.path.exists(file_path):
        logger.error(f"File not found: {file_path}")
        return
    
    try:
        # 初始化数据库
        logger.info("Initializing database...")
        await init_database()
        
        # 处理文件
        saved_count = await process_net_value_file(file_path)
        
        if saved_count > 0:
            logger.info(f"✅ Successfully processed {saved_count} records")
            
            # 验证数据
            await verify_data_in_database("133030A")
        else:
            logger.error("❌ No records were saved")
        
    except Exception as e:
        logger.error(f"❌ Processing failed: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        # 关闭数据库连接
        await close_database()
        logger.info("Database connection closed")
    
    print("\n" + "=" * 50)
    print("处理完成！")


if __name__ == "__main__":
    asyncio.run(main())

"""
Financial Product Detail Crawler
"""

import json
import asyncio
from typing import Dict, List, Optional, Any, Tuple, Coroutine
from datetime import datetime
from urllib.parse import urlencode

from crawl4ai import BrowserConfig, AsyncWebCrawler, CrawlerRunConfig, CacheMode
from loguru import logger
import aiofiles
import os


class FinancialProductDetailCrawler:
    """理财产品详情爬虫"""

    def __init__(self, cdp_url: str = None, save_directory: str = "data"):
        """
        初始化详情爬虫
        
        Args:
            save_directory: 数据保存目录
        """
        self.session_id = f"detail_crawler_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        self.cdp_url = cdp_url
        self.save_directory = save_directory
        self.requests_dir = f"{save_directory}/requests"
        self.details_dir = f"{save_directory}/details"
        self.history_dir = f"{save_directory}/history"

        # 确保目录存在
        os.makedirs(self.requests_dir, exist_ok=True)
        os.makedirs(self.details_dir, exist_ok=True)
        os.makedirs(self.history_dir, exist_ok=True)

        # 招商银行理财产品详情页基础URL
        self.detail_page_base_url = (
            "https://mobile.cmbchina.com/IEntrustFinance/subsidiaryproduct/financedetail.html"
        )

        # 目标API接口
        # self.detail_api_url = "https://mobile.cmbchina.com/ientrustfinance/sa-finance-detail/prd-info"
        # self.history_api_url = "https://mobile.cmbchina.com/ientrustfinance/product-statistics/get-history-profit"

        # 目标API接口
        self.detail_apis = [
            "prd-info",
        ]
        self.chart_apis = [
            "get-history-profit", # 万份收益
            "get-history-net-value", # 单位净值
        ]
        self.target_apis = self.detail_apis + self.chart_apis

        # 存储爬取的数据
        self.product_details = {}
        self.history_profits = {}

        logger.info("FinancialProductDetailCrawler initialized")

    def _is_target_request(self, url: str) -> bool:
        """
        检查是否为目标请求URL

        Args:
            url: 请求URL

        Returns:
            bool: 是否为目标请求
        """
        return any(api in url.lower() for api in self.target_apis)

    def _is_detail_request(self, url: str) -> bool:
        """
        检查是否为目标请求URL

        Args:
            url: 请求URL

        Returns:
            bool: 是否为目标请求
        """
        return any(api in url.lower() for api in self.detail_apis)

    def _is_chart_request(self, url: str) -> tuple[bool, str]:
        """
        检查是否为目标请求URL，并返回匹配到的关键字

        Args:
            url: 请求URL

        Returns:
            tuple[bool, str]: 是否为目标请求，匹配到的关键字（未匹配到则为空字符串）
        """
        url_lower = url.lower()
        for api in self.chart_apis:
            if api in url_lower:
                return True, api
        return False, ""

    async def _save_json_data(self, data: Any, filename: str, directory: str = None) -> str:
        """
        保存JSON数据到文件
        
        Args:
            data: 要保存的数据
            filename: 文件名
            directory: 保存目录
            
        Returns:
            str: 保存的文件路径
        """
        if directory is None:
            directory = self.details_dir

        filepath = os.path.join(directory, filename)

        try:
            async with aiofiles.open(filepath, 'w', encoding='utf-8') as f:
                await f.write(json.dumps(data, ensure_ascii=False, indent=2))

            logger.info(f"Saved data to: {filepath}")
            return filepath
        except Exception as e:
            logger.error(f"Failed to save data to {filepath}: {e}")
            raise

    def _build_detail_url(self, rip_cod: str, saa_cod: str) -> str:
        """
        构建详情页URL
        
        Args:
            rip_cod: 产品代码
            saa_cod: 销售代码
            
        Returns:
            str: 详情页URL
        """
        params = {
            "popup": "false",
            "XRIPINN": rip_cod,
            "XSAACOD": saa_cod
        }
        return f"{self.detail_page_base_url}?{urlencode(params)}"

    async def _extract_detail_from_response(self, response_text: str) -> Optional[Dict[str, Any]]:
        """
        从响应文本中提取产品详情数据
        
        Args:
            response_text: 响应文本
            
        Returns:
            Optional[Dict]: 产品详情数据
        """
        try:
            data = json.loads(response_text)

            if data.get("sysCode") == 200 and "bizResult" in data:
                biz_result = data["bizResult"]
                if biz_result.get("code") == 200 and "data" in biz_result:
                    detail_data = biz_result["data"]
                    logger.info("Extracted product detail from response")
                    return detail_data

            logger.warning("No valid detail data found in response")
            return None

        except json.JSONDecodeError as e:
            logger.error(f"Failed to parse JSON response: {e}")
            return None
        except Exception as e:
            logger.error(f"Failed to extract detail from response: {e}")
            return None

    async def _extract_history_from_response(self, response_text: str) -> Optional[List[Dict[str, Any]]]:
        """
        从响应文本中提取历史收益数据
        
        Args:
            response_text: 响应文本
            
        Returns:
            Optional[List[Dict]]: 历史收益数据
        """
        try:
            data = json.loads(response_text)

            if data.get("sysCode") == 200 and "bizResult" in data:
                biz_result = data["bizResult"]
                if biz_result.get("code") == 200 and "data" in biz_result:
                    history_data = biz_result["data"]
                    logger.info(f"Extracted {len(history_data)} history profit records from response")
                    return history_data

            logger.warning("No valid history data found in response")
            return None

        except json.JSONDecodeError as e:
            logger.error(f"Failed to parse JSON response: {e}")
            return None
        except Exception as e:
            logger.error(f"Failed to extract history from response: {e}")
            return None

    async def crawl_product_detail(self, rip_cod: str, saa_cod: str, wait_time: int = 15) -> None | tuple[
        dict[str, Any] | None, dict[Any, Any]] | tuple[None, None]:
        """
        爬取单个产品的详情和历史收益
        
        Args:
            rip_cod: 产品代码
            saa_cod: 销售代码
            wait_time: 等待时间（秒）
            
        Returns:
            Tuple[Optional[Dict], Optional[Dict]]: (产品详情, 历史收益)
        """

        detail_url = self._build_detail_url(rip_cod, saa_cod)
        captured_requests = []

        logger.info(f"Starting to crawl product detail: {rip_cod}, url:{detail_url}")

        try:
            # 配置浏览器选项
            browser_config = BrowserConfig(
                cdp_url=self.cdp_url,
                headless=False,  # 显示浏览器以便调试
                java_script_enabled=True,

                # viewport_width=414,
                # viewport_height=896,
                # user_agent="Mozilla/5.0 (iPhone; CPU iPhone OS 14_7_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.1.2 Mobile/15E148 Safari/604.1",
                # extra_args=[
                #     "--no-sandbox",
                #     "--disable-dev-shm-usage",
                #     "--disable-blink-features=AutomationControlled",
                #     "--disable-web-security"
                # ]
            )
            async with AsyncWebCrawler(config=browser_config) as crawler:
                # JavaScript代码用于监听网络请求和模拟用户交互
                # js_code = f"""
                # // 等待页面加载完成
                # await new Promise(resolve => {{
                #     if (document.readyState === 'complete') {{
                #         resolve();
                #     }} else {{
                #         window.addEventListener('load', resolve);
                #     }}
                # }});
                #
                # console.log('Detail page loaded, starting network monitoring');
                #
                # // 存储捕获的请求
                # const capturedRequests = [];
                # const detailApiUrl = '{self.detail_api_url}';
                # const historyApiUrl = '{self.history_api_url}';
                #
                # // 重写XMLHttpRequest
                # const originalXHR = window.XMLHttpRequest;
                # window.XMLHttpRequest = function() {{
                #     const xhr = new originalXHR();
                #     const originalOpen = xhr.open;
                #     const originalSend = xhr.send;
                #
                #     xhr.open = function(method, url, ...args) {{
                #         this._method = method;
                #         this._url = url;
                #         this._startTime = Date.now();
                #         return originalOpen.apply(this, [method, url, ...args]);
                #     }};
                #
                #     xhr.send = function(data) {{
                #         const self = this;
                #         this.addEventListener('readystatechange', function() {{
                #             if (this.readyState === 4) {{
                #                 const requestInfo = {{
                #                     method: self._method,
                #                     url: self._url,
                #                     status: this.status,
                #                     response: this.responseText,
                #                     timestamp: new Date().toISOString(),
                #                     duration: Date.now() - self._startTime,
                #                     type: 'xhr'
                #                 }};
                #
                #                 // 只保存目标URL的请求
                #                 if (self._url.includes(detailApiUrl) || self._url.includes(historyApiUrl)) {{
                #                     capturedRequests.push(requestInfo);
                #                     console.log('Captured target XHR:', self._url);
                #                 }}
                #             }}
                #         }});
                #         return originalSend.apply(this, [data]);
                #     }};
                #
                #     return xhr;
                # }};
                #
                # // 重写fetch
                # const originalFetch = window.fetch;
                # window.fetch = function(url, options = {{}}) {{
                #     const startTime = Date.now();
                #
                #     return originalFetch(url, options).then(async response => {{
                #         if (url.includes(detailApiUrl) || url.includes(historyApiUrl)) {{
                #             const clonedResponse = response.clone();
                #             try {{
                #                 const text = await clonedResponse.text();
                #                 const requestInfo = {{
                #                     method: options.method || 'GET',
                #                     url: url,
                #                     status: response.status,
                #                     response: text,
                #                     timestamp: new Date().toISOString(),
                #                     duration: Date.now() - startTime,
                #                     type: 'fetch'
                #                 }};
                #                 capturedRequests.push(requestInfo);
                #                 console.log('Captured target Fetch:', url);
                #             }} catch (e) {{
                #                 console.error('Failed to capture fetch response:', e);
                #             }}
                #         }}
                #         return response;
                #     }});
                # }};
                #
                # // 等待页面完全加载
                # await new Promise(resolve => setTimeout(resolve, 3000));
                #
                # // 尝试点击"万份收益"按钮或相关元素
                # const profitButtons = document.querySelectorAll('button, .btn, .tab, .profit-btn, [data-tab]');
                # for (const btn of profitButtons) {{
                #     const text = btn.textContent || btn.innerText || '';
                #     if (text.includes('万份收益') || text.includes('收益') || text.includes('历史')) {{
                #         try {{
                #             btn.click();
                #             console.log('Clicked profit button:', text);
                #             await new Promise(resolve => setTimeout(resolve, 2000));
                #         }} catch (e) {{
                #             console.log('Failed to click button:', e);
                #         }}
                #     }}
                # }}
                #
                # // 尝试滚动页面以触发更多请求
                # window.scrollTo(0, document.body.scrollHeight);
                # await new Promise(resolve => setTimeout(resolve, 2000));
                #
                # // 再次尝试点击可能的按钮
                # const moreButtons = document.querySelectorAll('*');
                # for (const element of moreButtons) {{
                #     const text = element.textContent || element.innerText || '';
                #     if (text.includes('万份收益') && element.tagName !== 'BODY' && element.tagName !== 'HTML') {{
                #         try {{
                #             element.click();
                #             console.log('Clicked element with profit text:', text);
                #             await new Promise(resolve => setTimeout(resolve, 2000));
                #             break;
                #         }} catch (e) {{
                #             // 忽略点击失败
                #         }}
                #     }}
                # }}
                #
                # // 最后等待一段时间确保所有请求完成
                # await new Promise(resolve => setTimeout(resolve, 3000));
                #
                # console.log(`Final captured requests count: ${{capturedRequests.length}}`);
                #
                # // 将捕获的请求存储到全局变量
                # window.capturedRequests = capturedRequests;
                #
                # return capturedRequests;
                # """

                run_config = CrawlerRunConfig(
                    session_id=self.session_id,
                    cache_mode=CacheMode.BYPASS,
                    # virtual_scroll_config=virtual_config,

                    capture_network_requests=True,
                    # wait_for="css:.athing:nth-child(30)",  # 等待第30个元素出现
                    wait_for="""js:() => {
                        //await new Promise(resolve => setTimeout(resolve, 1000));
                        const topTabItems = document.querySelectorAll('.top-tab .item');
                        if (topTabItems.length >= 2) {
                            // 第二个选项卡是"万份收益" or "单位净值"
                            topTabItems[1].click();
                            return true;
                        }
                        return false;
                    }    
                    """,
                    delay_before_return_html=5.0,

                    # js_only = True, #不打开新page
                    # extraction_strategy=llm_strategy,

                    # scan_full_page = True, # 自动滚动
                    # scroll_delay = 1.0, # 滚动延迟
                    # max_scroll_steps=20, # 限制滚动次数
                )

                # 执行爬取
                result = await crawler.arun(
                    url=detail_url,
                    config=run_config,
                )

                logger.info("Crawling completed, processing network requests")

                # 处理捕获的网络请求
                if hasattr(result, 'network_requests') and result.network_requests:
                    logger.info(f"Captured {len(result.network_requests)} network requests")

                    detail_data, history_data = await self._process_network_requests(rip_cod, result.network_requests)

                    # for request in result.network_requests:
                    #     await self._process_network_request(request)

                    # 存储到内存中
                    if detail_data:
                        key = f"{rip_cod}_{saa_cod}"
                        self.product_details[key] = detail_data

                    if history_data:
                        key = f"{rip_cod}_{saa_cod}"
                        self.history_profits[key] = history_data

                    logger.info(
                        f"Completed crawling for {rip_cod}: detail={'Yes' if detail_data else 'No'}, history={'Yes' if history_data else 'No'}")

                    return detail_data, history_data
                else:
                    logger.warning("No network requests captured")

                # # 从页面获取捕获的请求
                # if hasattr(result, 'js_execution_result') and result.js_execution_result:
                #     captured_requests = result.js_execution_result

                logger.info(f"Detail crawling completed. Captured {len(captured_requests)} requests")

        except Exception as e:
            logger.error(f"Failed to crawl product detail for {rip_cod}: {e}")
            return None, None

    async def crawl_multiple_products(self, product_codes: List[Tuple[str, str]],
                                      wait_time: int = 15,
                                      delay_between_requests: int = 3) -> Dict[str, Any]:
        """
        爬取多个产品的详情
        
        Args:
            product_codes: 产品代码列表 [(rip_cod, saa_cod), ...]
            wait_time: 每个产品的等待时间（秒）
            delay_between_requests: 请求间延迟（秒）
            
        Returns:
            Dict: 包含所有产品详情和历史收益的字典
        """
        logger.info(f"Starting to crawl {len(product_codes)} products")

        results = {
            "details": {},
            "history": {},
            "success_count": 0,
            "failed_count": 0,
            "crawl_time": datetime.now().isoformat()
        }

        for i, (rip_cod, saa_cod) in enumerate(product_codes):
            try:
                logger.info(f"Crawling product {i + 1}/{len(product_codes)}: {rip_cod}")

                detail_data, history_data = await self.crawl_product_detail(rip_cod, saa_cod, wait_time)

                key = f"{rip_cod}_{saa_cod}"
                if detail_data:
                    results["details"][key] = detail_data
                if history_data:
                    results["history"][key] = history_data

                if detail_data or history_data:
                    results["success_count"] += 1
                else:
                    results["failed_count"] += 1

                # 添加延迟避免请求过快
                if i < len(product_codes) - 1:  # 最后一个不需要延迟
                    await asyncio.sleep(delay_between_requests)

            except Exception as e:
                logger.error(f"Failed to crawl product {rip_cod}: {e}")
                results["failed_count"] += 1

        logger.info(f"Completed crawling: {results['success_count']} success, {results['failed_count']} failed")

        # 保存汇总结果
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        summary_filename = f"crawl_summary_{timestamp}.json"
        await self._save_json_data(results, summary_filename, self.details_dir)

        return results

    async def get_product_details(self) -> Dict[str, Dict[str, Any]]:
        """获取所有产品详情"""
        return self.product_details.copy()

    async def get_history_profits(self) -> Dict[str, List[Dict[str, Any]]]:
        """获取所有历史收益数据"""
        return self.history_profits.copy()

    async def _process_network_requests(self, rip_cod: str, network_events: List[Dict[str, Any]]):

        # 保存请求数据
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S_%f")
        request_filename = f"network_events_detail_{timestamp}.json"
        await self._save_json_data(network_events, request_filename, self.requests_dir)

        requests = []
        responses = []
        failures = []

        for event in network_events:
            event_type = event.get("event_type")
            if event_type == "request":
                requests.append(event)
            elif event_type == "response":
                if event.get("headers") and event.get("headers").get("content-type", "").startswith("application/json"):
                    responses.append(event)
            elif event_type == "request_failed":
                failures.append(event)

        print(f"Captured {len(requests)} requests, {len(responses)} responses, and {len(failures)} failures")



        # 处理捕获的请求
        detail_data = None
        history_data = {}  # 是多种类型

        for i, response in enumerate(responses):
            try:
                # 保存请求数据
                # timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                # filename = f"detail_request_{rip_cod}_{timestamp}_{i}.json"
                # await self._save_json_data(response, filename, self.requests_dir)

                url = response.get('url', '')
                status_code = response.get('status', 0)
                status_text = response.get('status_text', '')
                response_body = response['body']['text']

                logger.info(f"Processing request: {url} (status: {status_code}-{status_text})")

                # 产品详情数据
                if self._is_detail_request(url):
                    detail_data = await self._extract_detail_from_response(response_body)
                    if detail_data:
                        # 保存详情数据
                        detail_filename = f"product_detail_{rip_cod}_{timestamp}.json"
                        await self._save_json_data(detail_data, detail_filename, self.details_dir)

                # 历史收益数据
                is_chart, chart_name = self._is_chart_request(url)
                if is_chart:
                    chart_data = await self._extract_history_from_response(response_body)
                    if chart_data:
                        history_data[chart_name] = chart_data
                        # 保存历史收益数据
                        history_filename = f"history_profit_{chart_name}_{rip_cod}_{timestamp}.json"
                        await self._save_json_data(history_data, history_filename, self.history_dir)

            except Exception as e:
                logger.error(f"Failed to process request {i} for {rip_cod}: {e}")
        return detail_data, history_data

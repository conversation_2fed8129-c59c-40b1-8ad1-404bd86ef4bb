"""
Basic usage examples for the financial product crawler
"""

import asyncio
import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

from src.nm_crawl.main import CrawlerApp
from src.nm_crawl.config.settings import AppConfig
from src.nm_crawl.utils.logger import setup_logging


async def example_crawl_list():
    """示例：爬取产品列表"""
    print("=== 爬取产品列表示例 ===")
    
    # 初始化应用
    app = CrawlerApp("config.json")
    await app.initialize()
    
    try:
        # 爬取产品列表
        result = await app.crawl_list(wait_time=15, max_scroll_attempts=3)
        
        print(f"成功爬取 {result['products_count']} 个产品")
        
        # 显示前几个产品信息
        products = result['products'][:3]  # 只显示前3个
        for i, product in enumerate(products, 1):
            print(f"\n产品 {i}:")
            print(f"  代码: {product.get('ripCod')}")
            print(f"  名称: {product.get('ripSnm')}")
            print(f"  收益率: {product.get('prdRat')}")
            print(f"  风险等级: {product.get('zylTag')}")
        
    finally:
        await app.cleanup()


async def example_crawl_details():
    """示例：爬取产品详情"""
    print("\n=== 爬取产品详情示例 ===")
    
    # 示例产品代码（实际使用时应该从列表爬取获得）
    product_codes = [
        ("GY030111", "D07"),  # 工银添利宝现金28
        # ("8997A", "D07"),     # 日日金57号A
    ]
    
    app = CrawlerApp("config.json")
    await app.initialize()
    
    try:
        result = await app.crawl_details(
            product_codes=product_codes,
            wait_time=10,
            delay=2
        )
        
        print(f"成功爬取 {result['details_count']} 个产品详情")
        print(f"成功爬取 {result['history_count']} 个历史收益记录")
        
    finally:
        await app.cleanup()


async def example_full_crawl():
    """示例：完整爬取 + 数据库存储"""
    print("\n=== 完整爬取示例 ===")

    app = CrawlerApp("config.json")
    await app.initialize()

    try:
        result = await app.crawl_full(max_products=5)  # 限制为5个产品以节省时间

        print(f"完整爬取结果:")
        print(f"  产品列表: {result['products_count']} 个")
        print(f"  产品详情: {result['details_count']} 个")
        print(f"  历史收益: {result['history_count']} 个")

        storage_result = result.get('storage_result', {})
        if storage_result:
            print(f"  数据库保存: {storage_result.get('success', False)}")

        # 额外的数据库处理
        print("\n=== 数据库处理 ===")
        try:
            from src.nm_crawl.services.data_processor import DataProcessor

            processor = DataProcessor("data")

            # 获取处理前的统计信息
            before_stats = await processor.get_database_stats()
            print(f"处理前数据库统计: 产品 {before_stats['products']} 个，历史记录 {before_stats['history_records']} 条")

            # 处理所有数据
            db_results = await processor.process_all_data()

            # 获取处理后的统计信息
            after_stats = await processor.get_database_stats()
            print(f"处理后数据库统计: 产品 {after_stats['products']} 个，历史记录 {after_stats['history_records']} 条")

            print(f"数据库写入结果:")
            print(f"  产品列表: {db_results['products']} 个")
            print(f"  产品详情: {db_results['details']} 个")
            print(f"  历史记录: {db_results['history']} 条")

            print("\n💾 数据已成功写入数据库: data/financial_products.db")
            print("💡 您可以使用SQL工具查询数据库中的数据")

        except Exception as e:
            print(f"数据库处理失败: {e}")
            import traceback
            traceback.print_exc()

    finally:
        await app.cleanup()


async def example_custom_config():
    """示例：使用自定义配置"""
    print("\n=== 自定义配置示例 ===")
    
    # 创建自定义配置
    custom_config = AppConfig(
        debug=True,
        crawler={
            "list_wait_time": 10,
            "detail_wait_time": 8,
            "max_detail_products": 3,
            "headless": False  # 显示浏览器窗口
        },
        logging={
            "level": "DEBUG",
            "log_directory": "logs/debug_example"
        },
        storage={
            "data_directory": "data/example"
        }
    )
    
    # 保存自定义配置
    from src.nm_crawl.config.settings import save_config
    config_file = save_config(custom_config, "example_config.json")
    print(f"保存自定义配置到: {config_file}")
    
    # 使用自定义配置
    app = CrawlerApp("example_config.json")
    await app.initialize()
    
    try:
        # 爬取少量数据用于演示
        result = await app.crawl_list(wait_time=10, max_scroll_attempts=2)
        print(f"使用自定义配置爬取了 {result['products_count']} 个产品")
        
    finally:
        await app.cleanup()


async def example_monitoring():
    """示例：监控和状态查看"""
    print("\n=== 监控示例 ===")
    
    app = CrawlerApp("config.json")
    await app.initialize()
    
    try:
        # 执行一些爬取操作
        await app.crawl_list(wait_time=10, max_scroll_attempts=2)
        
        # 查看应用状态
        status = app.get_status()
        
        print("应用状态:")
        print(f"  数据库: {status['config']['database_url']}")
        print(f"  数据目录: {status['config']['data_directory']}")
        print(f"  健康状态: {'健康' if status['health']['is_healthy'] else '不健康'}")
        
        metrics = status['metrics']
        print(f"\n监控指标:")
        print(f"  总爬取次数: {metrics.get('total_crawls', 0)}")
        print(f"  成功率: {metrics.get('success_rate', 0)}%")
        print(f"  总产品数: {metrics.get('total_products', 0)}")
        print(f"  最后爬取时间: {metrics.get('last_crawl_time', '从未')}")
        
        # 保存监控报告
        if app.monitor:
            report_file = app.monitor.save_metrics_report()
            print(f"  监控报告已保存到: {report_file}")
        
    finally:
        await app.cleanup()


def example_scheduler_config():
    """示例：调度器配置"""
    print("\n=== 调度器配置示例 ===")
    
    # 创建启用调度器的配置
    scheduler_config = AppConfig(
        scheduler={
            "enable_scheduler": True,
            "full_crawl_interval": "daily",
            "list_crawl_interval": "4h",
            "full_crawl_time": "09:00",
            "auto_start": True
        },
        crawler={
            "max_detail_products": 20,
            "headless": True
        }
    )
    
    # 保存配置
    from src.nm_crawl.config.settings import save_config
    config_file = save_config(scheduler_config, "scheduler_config.json")
    print(f"调度器配置已保存到: {config_file}")
    
    print("调度器配置说明:")
    print("  - 每天09:00执行完整爬取")
    print("  - 每4小时执行列表爬取")
    print("  - 最多爬取20个产品详情")
    print("  - 使用无头浏览器模式")
    
    print("\n启动调度器命令:")
    print(f"  nm-crawl -c {config_file} start-scheduler")


async def main():
    """主函数"""
    print("银行理财产品爬虫使用示例")
    print("=" * 50)
    
    # 设置基本日志
    setup_logging(level="INFO")
    
    try:
        # 运行各种示例
        # await example_crawl_list()
        # await example_crawl_details()
        await example_full_crawl()
        # await example_custom_config()
        # await example_monitoring()
        
        # 非异步示例
        example_scheduler_config()
        
        print("\n所有示例执行完成！")
        print("\n更多用法请参考:")
        print("  - README.md 文档")
        print("  - nm-crawl --help 命令帮助")
        print("  - config.json 配置文件")
        
    except Exception as e:
        print(f"示例执行出错: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    # 运行示例
    asyncio.run(main())

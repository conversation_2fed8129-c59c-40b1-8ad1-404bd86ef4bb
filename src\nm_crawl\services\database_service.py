"""
数据库服务类 - 处理招商银行理财产品数据的写入和查询
"""

import json
from datetime import datetime
from typing import List, Dict, Any
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, update, delete
from sqlalchemy.dialects.sqlite import insert
from sqlalchemy.exc import IntegrityError
from loguru import logger

from ..models.financial_models import ProductDetail, HistoryProfits, ProductList
from ..database.connection import get_db_session
from ..utils.data_processor import DataProcessor as DataCleaner


class CmbDatabaseService:
    """招商银行理财产品数据库服务"""
    
    def __init__(self):
        self.bank_prefix = "cmb_"
        self.data_cleaner = DataCleaner()
    
    def _generate_product_id(self, rip_cod: str) -> str:
        """生成产品ID: cmb_ + ripCod"""
        return f"{self.bank_prefix}{rip_cod}"
    

    
    async def save_product_list(self, products: List[Dict[str, Any]], crawl_time: datetime = None) -> int:
        """
        保存产品列表数据
        
        Args:
            products: 产品列表数据
            crawl_time: 爬取时间
            
        Returns:
            int: 保存的产品数量
        """
        if not products:
            return 0
        
        if crawl_time is None:
            crawl_time = datetime.now()
        
        saved_count = 0
        
        async for session in get_db_session():
            try:
                for product_data in products:
                    rip_cod = product_data.get('ripCod')
                    if not rip_cod:
                        logger.warning(f"Product missing ripCod: {product_data}")
                        continue
                    
                    product_id = self._generate_product_id(rip_cod)
                    
                    # 使用INSERT OR REPLACE语法处理产品列表数据
                    stmt = insert(ProductList).values(
                        product_id=product_id,
                        rip_cod=rip_cod,
                        saa_cod=product_data.get('saaCod'),
                        rip_snm=product_data.get('ripSnm'),
                        fnd_nbr=product_data.get('fndNbr'),
                        prd_rat=product_data.get('prdRat'),
                        rat_des=product_data.get('ratDes'),
                        zyl_tag=product_data.get('zylTag'),
                        prd_inf=product_data.get('prdInf'),
                        ter_day=product_data.get('terDay'),
                        new_flg=product_data.get('newFlg'),
                        sell_out=product_data.get('sellOut'),
                        dxs_tag=product_data.get('dxsTag'),
                        jjb_tag=product_data.get('jjbTag'),
                        sal_tim=product_data.get('salTim'),
                        prf_open_tag=product_data.get('prfOpenTag'),
                        prd_tags=product_data.get('prdTags'),
                        raw_data=product_data,
                        crawl_time=crawl_time
                    )

                    # 在冲突时替换现有记录
                    stmt = stmt.on_conflict_do_update(
                        index_elements=['product_id'],
                        set_=dict(
                            rip_snm=stmt.excluded.rip_snm,
                            prd_rat=stmt.excluded.prd_rat,
                            rat_des=stmt.excluded.rat_des,
                            zyl_tag=stmt.excluded.zyl_tag,
                            prd_inf=stmt.excluded.prd_inf,
                            ter_day=stmt.excluded.ter_day,
                            new_flg=stmt.excluded.new_flg,
                            sell_out=stmt.excluded.sell_out,
                            prd_tags=stmt.excluded.prd_tags,
                            raw_data=stmt.excluded.raw_data,
                            crawl_time=stmt.excluded.crawl_time,
                            updated_at=stmt.excluded.updated_at
                        )
                    )

                    await session.execute(stmt)
                    saved_count += 1
                
                await session.commit()
                logger.info(f"Successfully saved {saved_count} products to database")
                return saved_count

            except Exception as e:
                await session.rollback()
                logger.error(f"Failed to save product list: {e}")
                raise
    
    async def save_product_detail(self, detail_data: Dict[str, Any], crawl_time: datetime = None) -> bool:
        """
        保存产品详情数据
        
        Args:
            detail_data: 产品详情数据
            crawl_time: 爬取时间
            
        Returns:
            bool: 是否保存成功
        """
        if not detail_data:
            return False
        
        if crawl_time is None:
            crawl_time = datetime.now()
        
        # 从详情数据中获取产品代码
        rip_cod = detail_data.get('ripInn') or detail_data.get('ripNbr') or detail_data.get('ripCod')
        if not rip_cod:
            logger.warning(f"Product detail missing ripCod: {detail_data}")
            return False
        
        product_id = self._generate_product_id(rip_cod)
        
        async for session in get_db_session():
            try:

                
                # 使用INSERT OR REPLACE语法处理产品详情数据
                stmt = insert(ProductDetail).values(
                    product_id=product_id,
                    rip_cod=rip_cod,
                    saa_cod=detail_data.get('saaCod'),
                    rip_snm=detail_data.get('ripSnm'),
                    crp_nam=detail_data.get('crpNam'),
                    crp_cod=detail_data.get('crpCod'),
                    rate_text=self.data_cleaner.clean_rate(detail_data.get('rateText')),  # 清理HTML标签
                    rate_name=detail_data.get('rateName'),
                    ter_day=detail_data.get('terDay'),
                    risk_lvl=detail_data.get('riskLvl'),
                    sbs_uqt=detail_data.get('sbsUqt'),
                    invest_typ=detail_data.get('investTyp'),
                    buy_time_rule=detail_data.get('buyTimeRule'),
                    pay_time_rule=detail_data.get('payTimeRule'),
                    buy_sum=detail_data.get('buySum'),
                    csh_prf=detail_data.get('cshPrf'),
                    features=detail_data.get('features'),
                    raw_data=detail_data,
                    crawl_time=crawl_time
                )

                # 在冲突时替换现有记录
                cleaned_rate_text = self.data_cleaner.clean_rate(detail_data.get('rateText'))
                stmt = stmt.on_conflict_do_update(
                    index_elements=['product_id'],
                    set_=dict(
                        rip_snm=stmt.excluded.rip_snm,
                        crp_nam=stmt.excluded.crp_nam,
                        rate_text=cleaned_rate_text,  # 使用清理后的收益率文本
                        rate_name=stmt.excluded.rate_name,
                        ter_day=stmt.excluded.ter_day,
                        risk_lvl=stmt.excluded.risk_lvl,
                        sbs_uqt=stmt.excluded.sbs_uqt,
                        invest_typ=stmt.excluded.invest_typ,
                        buy_time_rule=stmt.excluded.buy_time_rule,
                        pay_time_rule=stmt.excluded.pay_time_rule,
                        buy_sum=stmt.excluded.buy_sum,
                        csh_prf=stmt.excluded.csh_prf,
                        features=stmt.excluded.features,
                        raw_data=stmt.excluded.raw_data,
                        crawl_time=stmt.excluded.crawl_time,
                        updated_at=stmt.excluded.updated_at
                    )
                )

                await session.execute(stmt)
                await session.commit()
                
                logger.info(f"Successfully saved product detail for {rip_cod}")
                return True
                
            except Exception as e:
                await session.rollback()
                logger.error(f"Failed to save product detail for {rip_cod}: {e}")
                raise
        
        return False
    
    async def save_product_history(self, rip_cod: str, history_data: List[Dict[str, Any]], crawl_time: datetime = None) -> int:
        """
        保存产品历史收益数据
        
        Args:
            rip_cod: 产品代码
            history_data: 历史收益数据列表
            crawl_time: 爬取时间
            
        Returns:
            int: 保存的历史记录数量
        """
        if not history_data or not rip_cod:
            return 0
        
        if crawl_time is None:
            crawl_time = datetime.now()
        
        product_id = self._generate_product_id(rip_cod)
        saved_count = 0
        
        async for session in get_db_session():
            try:
                for history_item in history_data:
                    profit_date = history_item.get('date')
                    if not profit_date:
                        continue

                    ten_thousand_profit = history_item.get('tenThousandProfit')
                    seven_days_annual_profit = history_item.get('sevenDaysAnnualProfit')

                    # 使用SQLite的INSERT OR REPLACE语法来处理重复数据
                    stmt = insert(HistoryProfits).values(
                        product_id=product_id,
                        rip_cod=rip_cod,
                        profit_date=profit_date,
                        ten_thousand_profit=ten_thousand_profit,
                        seven_days_annual_profit=seven_days_annual_profit,
                        crawl_time=crawl_time
                    )

                    # 在冲突时替换现有记录
                    stmt = stmt.on_conflict_do_update(
                        index_elements=['product_id', 'profit_date'],
                        set_=dict(
                            ten_thousand_profit=stmt.excluded.ten_thousand_profit,
                            seven_days_annual_profit=stmt.excluded.seven_days_annual_profit,
                            crawl_time=stmt.excluded.crawl_time
                        )
                    )

                    await session.execute(stmt)
                    saved_count += 1

                await session.commit()
                logger.info(f"Successfully saved {saved_count} history records for {rip_cod}")
                return saved_count

            except Exception as e:
                await session.rollback()
                logger.error(f"Failed to save product history for {rip_cod}: {e}")
                raise
    
    async def get_product_count(self) -> int:
        """获取产品总数"""
        async for session in get_db_session():
            try:
                result = await session.execute(select(ProductList))
                return len(result.scalars().all())
            except Exception as e:
                logger.error(f"Failed to get product count: {e}")
                return 0

    async def get_history_count(self) -> int:
        """获取历史记录总数"""
        async for session in get_db_session():
            try:
                result = await session.execute(select(HistoryProfits))
                return len(result.scalars().all())
            except Exception as e:
                logger.error(f"Failed to get history count: {e}")
                return 0

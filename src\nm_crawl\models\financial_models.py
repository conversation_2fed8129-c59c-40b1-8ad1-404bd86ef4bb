"""
理财产品数据库模型 - 通用模型定义
支持多家银行的理财产品数据存储
"""

from datetime import datetime
from typing import Optional, List
from sqlalchemy import Column, Integer, String, Text, DateTime, Boolean, JSON, Index, UniqueConstraint
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.sql import func

Base = declarative_base()


class ProductDetail(Base):
    """理财产品详情表"""
    __tablename__ = "product_details"
    
    # 主键：bank_prefix + ripCod (如: cmb_8997A, icbc_ABC123)
    product_id = Column(String(100), primary_key=True, comment="产品ID (bank_prefix+ripCod)")

    # 基础信息
    rip_cod = Column(String(50), nullable=False, index=True, comment="产品代码")
    saa_cod = Column(String(50), comment="销售代码")
    rip_snm = Column(String(200), nullable=False, comment="产品名称")

    # 发行机构信息
    crp_nam = Column(String(200), comment="发行机构名称")
    crp_cod = Column(String(50), comment="发行机构代码")

    # 收益率信息
    rate_text = Column(String(100), comment="收益率显示文本")
    rate_name = Column(String(50), comment="收益率名称")

    # 产品基本信息
    ter_day = Column(String(200), comment="投资期限描述")
    risk_lvl = Column(String(50), comment="风险等级")
    sbs_uqt = Column(String(100), comment="起购金额")
    invest_typ = Column(String(50), comment="投资类型")

    # 交易规则
    buy_time_rule = Column(Text, comment="销售时间规则")
    pay_time_rule = Column(Text, comment="到账时间规则")

    # 其他重要信息
    buy_sum = Column(String(50), comment="购买人数")
    csh_prf = Column(String(200), comment="现金收益描述")

    # 标签和特性
    features = Column(JSON, comment="产品特色列表")
    
    # 完整原始数据
    raw_data = Column(JSON, comment="完整原始数据")
    
    # 时间戳
    created_at = Column(DateTime, default=func.now(), comment="创建时间")
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now(), comment="更新时间")
    crawl_time = Column(DateTime, comment="爬取时间")
    
    def __repr__(self):
        return f"<ProductDetail(id='{self.product_id}', name='{self.rip_snm}')>"


class HistoryProfits(Base):
    """理财产品历史收益表"""
    __tablename__ = "history_profits"
    
    id = Column(Integer, primary_key=True, autoincrement=True, comment="自增主键")
    
    # 产品信息
    product_id = Column(String(100), nullable=False, index=True, comment="产品ID (bank_prefix+ripCod)")
    rip_cod = Column(String(50), nullable=False, index=True, comment="产品代码")
    
    # 收益数据
    profit_date = Column(String(20), nullable=False, comment="收益日期 (YYYY-MM-DD)")
    ten_thousand_profit = Column(String(20), comment="万份收益")
    seven_days_annual_profit = Column(String(20), comment="七日年化收益率")
    
    # 时间戳
    created_at = Column(DateTime, default=func.now(), comment="创建时间")
    crawl_time = Column(DateTime, comment="爬取时间")
    
    # 创建复合唯一索引
    __table_args__ = (
        UniqueConstraint('product_id', 'profit_date', name='uk_product_date'),
        Index('idx_rip_cod_date', 'rip_cod', 'profit_date'),
        Index('idx_profit_date', 'profit_date'),
    )
    
    def __repr__(self):
        return f"<HistoryProfits(product_id='{self.product_id}', date='{self.profit_date}')>"


class ProductList(Base):
    """理财产品列表表"""
    __tablename__ = "product_list"
    
    # 主键：bank_prefix + ripCod (如: cmb_8997A, icbc_ABC123)
    product_id = Column(String(100), primary_key=True, comment="产品ID (bank_prefix+ripCod)")
    
    # 基础信息
    rip_cod = Column(String(50), nullable=False, index=True, comment="产品代码")
    saa_cod = Column(String(50), comment="销售代码")
    rip_snm = Column(String(200), nullable=False, comment="产品名称")
    fnd_nbr = Column(String(50), comment="基金编号")
    
    # 收益率信息
    prd_rat = Column(String(20), comment="产品收益率")
    rat_des = Column(String(50), comment="收益率描述")
    
    # 产品信息
    zyl_tag = Column(String(100), comment="风险等级标签")
    prd_inf = Column(String(200), comment="产品信息")
    ter_day = Column(String(200), comment="投资期限")
    
    # 标签和状态
    new_flg = Column(String(10), comment="新产品标志")
    sell_out = Column(String(10), comment="售罄标志")
    dxs_tag = Column(String(50), comment="定向销售标签")
    jjb_tag = Column(String(50), comment="基金宝标签")
    sal_tim = Column(String(100), comment="销售时间")
    prf_open_tag = Column(Boolean, comment="收益开放标签")
    
    # 产品标签
    prd_tags = Column(JSON, comment="产品标签列表")
    
    # 完整原始数据
    raw_data = Column(JSON, comment="完整原始数据")
    
    # 时间戳
    created_at = Column(DateTime, default=func.now(), comment="创建时间")
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now(), comment="更新时间")
    crawl_time = Column(DateTime, comment="爬取时间")
    
    def __repr__(self):
        return f"<ProductList(id='{self.product_id}', name='{self.rip_snm}')>"


class CrawlLog(Base):
    """爬取日志表"""
    __tablename__ = "crawl_logs"
    
    id = Column(Integer, primary_key=True, autoincrement=True, comment="自增主键")
    
    # 爬取信息
    url = Column(String(500), nullable=False, comment="爬取URL")
    request_type = Column(String(20), nullable=False, comment="请求类型")
    status_code = Column(Integer, comment="状态码")
    response_data = Column(Text, comment="响应数据")
    
    # 处理状态
    is_processed = Column(Boolean, default=False, comment="是否已处理")
    error_message = Column(Text, comment="错误信息")
    
    # 时间戳
    created_at = Column(DateTime, default=func.now(), comment="创建时间")
    
    def __repr__(self):
        return f"<CrawlLog(url='{self.url}', request_type='{self.request_type}')>"

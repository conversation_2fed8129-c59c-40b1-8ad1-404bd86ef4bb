# 净值历史数据处理说明

## 概述

本项目新增了净值历史数据的处理功能，可以将JSON格式的净值数据导入到SQLite数据库中进行存储和查询。

## 数据库表结构

### history_netvalue 表

| 字段名 | 类型 | 说明 |
|--------|------|------|
| id | INTEGER | 自增主键 |
| product_id | VARCHAR(100) | 产品ID (bank_prefix+ripCod) |
| rip_cod | VARCHAR(50) | 产品代码 |
| net_value_date | VARCHAR(20) | 净值日期 (YYYY-MM-DD) |
| unit_net_value | VARCHAR(20) | 单位净值 |
| total_net_value | VARCHAR(20) | 累计净值 |
| net_value_change | VARCHAR(20) | 净值变化 |
| show_provision | BOOLEAN | 是否显示条款 |
| raw_data | JSON | 完整原始数据 |
| created_at | DATETIME | 创建时间 |
| crawl_time | DATETIME | 爬取时间 |

## 文件说明

### 1. 数据模型 (src/nm_crawl/models/financial_models.py)
- 新增 `HistoryNetValue` 类，定义净值历史数据的数据库模型
- 包含唯一索引和外键约束

### 2. 数据库服务 (src/nm_crawl/services/database_service.py)
- 新增 `save_net_value_history()` 方法，用于保存净值数据
- 新增 `get_net_value_count()` 方法，用于获取净值记录总数
- 支持数据去重和更新

### 3. 数据处理脚本 (process_netvalue_data.py)
- 处理JSON格式的净值历史数据文件
- 自动提取产品代码
- 批量写入数据库
- 数据验证和统计

### 4. 表创建脚本 (create_netvalue_table.py)
- 独立创建净值历史数据表
- 验证表结构

### 5. 查询脚本 (query_netvalue_database.py)
- 提供多种查询功能
- 统计信息查询
- 按产品查询
- 按日期范围查询
- 数据汇总

## 使用方法

### 1. 处理净值数据文件

```bash
python process_netvalue_data.py
```

该脚本会：
- 读取指定的JSON文件
- 提取产品代码
- 解析净值数据
- 写入数据库
- 验证数据完整性

### 2. 查询净值数据

```bash
python query_netvalue_database.py
```

该脚本会显示：
- 数据库统计信息
- 指定产品的净值数据
- 日期范围内的数据
- 数据汇总信息

### 3. 直接SQL查询

```bash
# 查看所有表
sqlite3 data/financial_products.db "SELECT name FROM sqlite_master WHERE type='table';"

# 查看净值记录总数
sqlite3 data/financial_products.db "SELECT COUNT(*) FROM history_netvalue;"

# 查看最新的净值数据
sqlite3 data/financial_products.db -header -column "SELECT product_id, rip_cod, net_value_date, unit_net_value, total_net_value, net_value_change FROM history_netvalue ORDER BY net_value_date DESC LIMIT 10;"

# 查看指定产品的净值数据
sqlite3 data/financial_products.db -header -column "SELECT * FROM history_netvalue WHERE rip_cod='133030A' ORDER BY net_value_date DESC;"
```

## 数据格式

### 输入JSON格式

```json
{
  "get-history-net-value": [
    {
      "date": "2025-08-21",
      "unitNetValue": "1.013600",
      "totalNetValue": "1.013600",
      "netValueChange": "0.03",
      "showProvision": false
    }
  ]
}
```

### 文件命名规范

文件名格式：`history_profit_get-history-net-value_{产品代码}_{日期}_{时间}_{随机数}.json`

例如：`history_profit_get-history-net-value_133030A_20250825_165906_764430.json`

## 特性

1. **数据去重**: 使用产品ID和日期的复合唯一索引防止重复数据
2. **数据更新**: 支持在冲突时更新现有记录
3. **完整性保证**: 保存完整的原始JSON数据
4. **索引优化**: 针对常用查询场景建立索引
5. **错误处理**: 完善的异常处理和日志记录

## 示例数据

成功处理的数据示例：

```
产品代码: 133030A
记录数: 30
日期范围: 2025-07-11 到 2025-08-21
净值范围: 1.010500 到 1.013600
```

## 注意事项

1. 确保数据库文件路径正确：`data/financial_products.db`
2. JSON文件格式必须符合预期结构
3. 产品代码从文件名中自动提取
4. 支持异步数据库操作，性能较好
5. 建议定期备份数据库文件

## 扩展功能

可以基于此功能进一步开发：
- 净值趋势分析
- 收益率计算
- 数据可视化
- 定时数据更新
- 多产品对比分析
